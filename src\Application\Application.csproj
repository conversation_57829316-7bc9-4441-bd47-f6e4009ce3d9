﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<AssemblyName>Zify.Settlement.$(MSBuildProjectName)</AssemblyName>
		<RootNamespace>$(AssemblyName)</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
	  <None Remove="Domain\UserWalletInformation.cs~RFae0e88.TMP" />
	</ItemGroup>

	<ItemGroup>
		<FrameworkReference Include="Microsoft.AspNetCore.App" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Ardalis.GuardClauses" Version="5.0.0" />
		<PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="DispatchR.Mediator" Version="1.2.0" />
		<PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
		<PackageReference Include="ErrorOr" Version="2.0.1" />
		<PackageReference Include="FluentValidation" Version="12.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="8.1.1" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.3" />
		<PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.7.3" />
	</ItemGroup>

</Project>
